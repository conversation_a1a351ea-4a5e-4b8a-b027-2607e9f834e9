/**
 * PaperControls.css
 * 
 * Styles for the paper controls component
 */

.rs-controls {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.rs-controls button {
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rs-add-btn {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  border-radius: 4px;
  opacity: 0.8;
}

.rs-add-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Loading and success states */
.rs-loading {
  position: relative;
  color: transparent !important;
}

.rs-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.rs-success {
  opacity: 1 !important;
  transform: scale(1.2) !important;
}