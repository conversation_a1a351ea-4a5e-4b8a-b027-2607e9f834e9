/**
 * PaperControls.css
 * 
 * Styles for the paper controls component
 */

.rs-controls {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.rs-controls button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.rs-add-btn {
  background-color: #4285f4;
  color: white;
}

.rs-add-btn:hover {
  background-color: #3367d6;
}

/* Loading and success states */
.rs-loading {
  position: relative;
  color: transparent !important;
}

.rs-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.rs-success {
  background-color: #34a853 !important;
} 