/**
 * PaperControls.js
 *
 * Component for paper controls (add button)
 */

class PaperControls {
  constructor(paperId, container) {
    this.paperId = paperId;
    this.container = container;
    this.element = null;
    this.addButton = null;
  }

  /**
   * Load CSS file for the paper controls
   * @private
   */
  _loadStyles() {
    const cssPath = chrome.runtime.getURL('content/ui/styles/PaperControls.css');
    if (!document.querySelector(`link[href="${cssPath}"]`)) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = cssPath;
      document.head.appendChild(link);
    }
  }

  /**
   * Initialize the paper controls
   * @param {Object} options - 配置选项
   * @param {Function} options.onAddToPaperBox - 点击添加到论文盒按钮的回调
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    this._loadStyles();
    this.element = this.createElement(options);
    this.container.appendChild(this.element);
  }

  /**
   * Create the paper controls element
   * @param {Object} options - 配置选项
   * @param {Function} options.onAddToPaperBox - 点击添加到论文盒按钮的回调
   * @returns {HTMLElement}
   */
  createElement(options = {}) {
    // Create controls container
    const container = document.createElement('div');
    container.className = 'rs-controls';
    container.dataset.paperId = this.paperId;

    // 创建"添加到论文盒"按钮
    this.addButton = document.createElement('button');
    this.addButton.className = 'rs-add-btn';
    this.addButton.dataset.paperId = this.paperId;
    this.addButton.title = '添加到论文盒';

    // 设置图标背景
    const addIconUrl = chrome.runtime.getURL('icons/add.png');
    this.addButton.style.backgroundImage = `url("${addIconUrl}")`;

    this.addButton.addEventListener('click', () => {
      if (options.onAddToPaperBox) {
        options.onAddToPaperBox(this.paperId);
      }
    });
    container.appendChild(this.addButton);

    return container;
  }

  /**
   * 显示添加成功状态
   */
  showAddSuccess() {
    if (!this.addButton) return;

    // 切换到"已添加"图标并保持
    const addedIconUrl = chrome.runtime.getURL('icons/added.png');
    this.addButton.style.backgroundImage = `url("${addedIconUrl}")`;
    this.addButton.title = '已添加到论文盒';
    this.addButton.classList.add('rs-success');
  }

  /**
   * Remove the paper controls
   */
  remove() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}

export default PaperControls; 